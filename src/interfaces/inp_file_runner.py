import numpy as np
import sys
sys.path.append('/home/<USER>/phd/tasks/nn_mat_model/src/utils')
from funcs import create_randomload_inpfile, create_monoload_inpfile

#%%
model_name = '../models/getstress.f90'

# material parameters
E = 200000.0e0
v = 0.3
sigma_y = 500.0
K = 0.0         # K=0 : perfect plasticity  |  {K=1000, m = 0.15} : isotropic hardening
eps_0 = 0.1
m = 0.15        # m=0 : perfect plasticity  |  {K=1000, m=1} : linear isotropic hardening
# --------------
load_type = 4
no_state = 3    # num of state variables

mat_el = np.array([E, v])
mat_hard = np.array([sigma_y, K, eps_0, m])
material = np.concatenate((mat_el, mat_hard))

folder = '/home/<USER>/phd/material_dvlp/driver/inp'   # the directory to save the output inp file
file_name = 'tmp_v1.0.inp'
data_type = 'random'        # TODO: selct the type of loading; options: 'random' or 'monotonic'

if data_type == 'random':
    no_inc = 1000            # num of random walk increments
    strain_range = 0.006    # NOTE absolute value (not in percentage)
    inp_creator_data = [material, no_inc, strain_range, model_name, no_state, load_type, folder, file_name]
    create_randomload_inpfile(*inp_creator_data)

elif data_type == 'monotonic':
    no_el_inc = 5; no_pl_inc = 4000; no_unload_inc = 0
    no_inc = [no_el_inc, no_pl_inc, no_unload_inc]
    inp_creator_data = [material, no_inc, model_name, no_state, load_type, folder, file_name]
    create_monoload_inpfile(*inp_creator_data)
