"""
SOLID Principles Demonstration: Open/Closed Principle (OCP)

This file demonstrates how to implement the Strategy pattern for learning rate scheduling,
making the system open for extension but closed for modification.

SOLID Principle: Open/Closed Principle (OCP)
- Software entities should be open for extension but closed for modification
- New learning rate schedules can be added without changing existing code
- Existing schedules remain untouched when adding new ones

Original Issue: In nn_model.py, the train_model method uses if/elif chains for different
learning rate schedules. Adding new schedules requires modifying the existing method.

Improvement: Use Strategy pattern to make learning rate scheduling extensible.

Date: 2025-07-22
Author: Augment Agent (Demonstration)
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from dataclasses import dataclass
import tensorflow as tf


# ============================================================================
# CONFIGURATION AND DATA STRUCTURES
# ============================================================================

@dataclass
class SchedulerConfig:
    """Base configuration for learning rate schedulers."""
    initial_learning_rate: float
    total_epochs: int


@dataclass
class ExponentialConfig(SchedulerConfig):
    """Configuration for exponential decay scheduler."""
    decay_rate: float = 0.75
    decay_fraction: float = 0.5  # Fraction of epochs over which to decay
    staircase: bool = False


@dataclass
class CosineConfig(SchedulerConfig):
    """Configuration for cosine decay scheduler."""
    first_decay_fraction: float = 0.2  # Fraction of epochs for first decay
    t_mul: float = 1.5  # Cycle length multiplier
    m_mul: float = 0.75  # Max learning rate multiplier
    alpha: float = 1e-5  # Minimum learning rate


@dataclass
class StepConfig(SchedulerConfig):
    """Configuration for step decay scheduler."""
    step_size: int = 100  # Epochs between drops
    gamma: float = 0.1  # Multiplicative factor of learning rate decay


@dataclass
class WarmupCosineConfig(SchedulerConfig):
    """Configuration for warmup + cosine decay scheduler."""
    warmup_epochs: int = 10
    min_learning_rate: float = 1e-6


# ============================================================================
# STRATEGY INTERFACE AND IMPLEMENTATIONS
# ============================================================================

class LearningRateScheduler(ABC):
    """
    Abstract base class for learning rate scheduling strategies.

    This interface ensures all schedulers provide consistent behavior
    while allowing for different implementation approaches.
    """

    @abstractmethod
    def create_scheduler(self, config: SchedulerConfig) -> tf.keras.optimizers.schedules.LearningRateSchedule:
        """
        Create a TensorFlow learning rate schedule.

        Args:
            config: Configuration object with scheduler parameters

        Returns:
            TensorFlow learning rate schedule object
        """
        pass

    @abstractmethod
    def get_name(self) -> str:
        """Return the name of this scheduling strategy."""
        pass

    def validate_config(self, config: SchedulerConfig) -> None:
        """
        Validate configuration parameters.

        Args:
            config: Configuration to validate

        Raises:
            ValueError: If configuration is invalid
        """
        if config.initial_learning_rate <= 0:
            raise ValueError("Initial learning rate must be positive")
        if config.total_epochs <= 0:
            raise ValueError("Total epochs must be positive")


class ConstantScheduler(LearningRateScheduler):
    """
    Strategy for constant learning rate (no scheduling).

    This is the simplest strategy that maintains a constant learning rate
    throughout training.
    """

    def create_scheduler(self, config: SchedulerConfig) -> float:
        """Return constant learning rate."""
        self.validate_config(config)
        return config.initial_learning_rate

    def get_name(self) -> str:
        return "constant"


class ExponentialScheduler(LearningRateScheduler):
    """
    Strategy for exponential decay learning rate scheduling.

    Implements exponential decay with configurable decay rate and timing.
    """

    def create_scheduler(self, config: ExponentialConfig) -> tf.keras.optimizers.schedules.ExponentialDecay:
        """Create exponential decay scheduler."""
        self.validate_config(config)

        decay_steps = int(config.total_epochs * config.decay_fraction)

        return tf.keras.optimizers.schedules.ExponentialDecay(
            initial_learning_rate=config.initial_learning_rate,
            decay_steps=decay_steps,
            decay_rate=config.decay_rate,
            staircase=config.staircase
        )

    def get_name(self) -> str:
        return "exponential"


class CosineScheduler(LearningRateScheduler):
    """
    Strategy for cosine decay with restarts.

    Implements cosine annealing with warm restarts for better convergence.
    """

    def create_scheduler(self, config: CosineConfig) -> tf.keras.optimizers.schedules.CosineDecayRestarts:
        """Create cosine decay scheduler with restarts."""
        self.validate_config(config)

        first_decay_steps = int(config.total_epochs * config.first_decay_fraction)

        return tf.keras.optimizers.schedules.CosineDecayRestarts(
            initial_learning_rate=config.initial_learning_rate,
            first_decay_steps=first_decay_steps,
            t_mul=config.t_mul,
            m_mul=config.m_mul,
            alpha=config.alpha
        )

    def get_name(self) -> str:
        return "cosine"


class StepScheduler(LearningRateScheduler):
    """
    Strategy for step-wise learning rate decay.

    Reduces learning rate by a factor at regular intervals.
    """

    def create_scheduler(self, config: StepConfig) -> tf.keras.optimizers.schedules.PiecewiseConstantDecay:
        """Create step decay scheduler."""
        self.validate_config(config)

        # Create boundaries and values for step decay
        boundaries = []
        values = [config.initial_learning_rate]

        current_lr = config.initial_learning_rate
        step = config.step_size

        while step < config.total_epochs:
            boundaries.append(step)
            current_lr *= config.gamma
            values.append(current_lr)
            step += config.step_size

        return tf.keras.optimizers.schedules.PiecewiseConstantDecay(
            boundaries=boundaries,
            values=values
        )

    def get_name(self) -> str:
        return "step"


class WarmupCosineScheduler(LearningRateScheduler):
    """
    Strategy for warmup followed by cosine decay.

    Demonstrates how complex scheduling strategies can be easily added
    without modifying existing code.
    """

    def create_scheduler(self, config: WarmupCosineConfig) -> tf.keras.optimizers.schedules.LearningRateSchedule:
        """Create warmup + cosine decay scheduler."""
        self.validate_config(config)

        # Custom scheduler combining warmup and cosine decay
        class WarmupCosineDecay(tf.keras.optimizers.schedules.LearningRateSchedule):
            def __init__(self, initial_lr, warmup_epochs, total_epochs, min_lr):
                self.initial_lr = initial_lr
                self.warmup_epochs = warmup_epochs
                self.total_epochs = total_epochs
                self.min_lr = min_lr

            def __call__(self, step):
                # Warmup phase
                warmup_lr = self.initial_lr * step / self.warmup_epochs

                # Cosine decay phase
                cosine_decay_epochs = self.total_epochs - self.warmup_epochs
                cosine_step = tf.maximum(0.0, step - self.warmup_epochs)
                cosine_lr = self.min_lr + 0.5 * (self.initial_lr - self.min_lr) * (
                    1 + tf.cos(tf.constant(3.14159) * cosine_step / cosine_decay_epochs)
                )

                return tf.where(step < self.warmup_epochs, warmup_lr, cosine_lr)

        return WarmupCosineDecay(
            config.initial_learning_rate,
            config.warmup_epochs,
            config.total_epochs,
            config.min_learning_rate
        )

    def get_name(self) -> str:
        return "warmup_cosine"


# ============================================================================
# SCHEDULER FACTORY (REGISTRY PATTERN)
# ============================================================================

class SchedulerFactory:
    """
    Factory for creating learning rate schedulers.

    This class demonstrates how the Registry pattern can be combined with
    the Strategy pattern to make the system even more extensible.

    Benefits:
    1. Easy registration of new schedulers
    2. Runtime discovery of available schedulers
    3. Type-safe scheduler creation
    4. Clear separation of concerns
    """

    def __init__(self):
        self._schedulers: Dict[str, LearningRateScheduler] = {}
        self._register_default_schedulers()

    def register_scheduler(self, name: str, scheduler: LearningRateScheduler) -> None:
        """
        Register a new scheduler strategy.

        Args:
            name: Name to register the scheduler under
            scheduler: Scheduler implementation
        """
        self._schedulers[name] = scheduler

    def create_scheduler(self, name: str, config: SchedulerConfig) -> tf.keras.optimizers.schedules.LearningRateSchedule:
        """
        Create a scheduler by name.

        Args:
            name: Name of the scheduler to create
            config: Configuration for the scheduler

        Returns:
            Configured learning rate schedule

        Raises:
            ValueError: If scheduler name is not registered
        """
        if name not in self._schedulers:
            available = list(self._schedulers.keys())
            raise ValueError(f"Unknown scheduler '{name}'. Available: {available}")

        return self._schedulers[name].create_scheduler(config)

    def get_available_schedulers(self) -> list:
        """Get list of available scheduler names."""
        return list(self._schedulers.keys())

    def _register_default_schedulers(self) -> None:
        """Register the default set of schedulers."""
        self.register_scheduler("constant", ConstantScheduler())
        self.register_scheduler("exponential", ExponentialScheduler())
        self.register_scheduler("cosine", CosineScheduler())
        self.register_scheduler("step", StepScheduler())
        self.register_scheduler("warmup_cosine", WarmupCosineScheduler())


# ============================================================================
# USAGE EXAMPLE AND COMPARISON
# ============================================================================

def demonstrate_ocp_improvement():
    """
    Demonstrates the improved OCP-compliant learning rate scheduling.

    Shows how new schedulers can be added without modifying existing code,
    and how the system is more flexible and maintainable.
    """
    print("=== Open/Closed Principle Demonstration ===")
    print()

    # Create factory
    factory = SchedulerFactory()

    print("Available schedulers:", factory.get_available_schedulers())
    print()

    # Example configurations
    configs = {
        "constant": SchedulerConfig(initial_learning_rate=1e-3, total_epochs=100),
        "exponential": ExponentialConfig(initial_learning_rate=1e-3, total_epochs=100, decay_rate=0.8),
        "cosine": CosineConfig(initial_learning_rate=1e-3, total_epochs=100),
        "step": StepConfig(initial_learning_rate=1e-3, total_epochs=100, step_size=30),
        "warmup_cosine": WarmupCosineConfig(initial_learning_rate=1e-3, total_epochs=100, warmup_epochs=10)
    }

    # Demonstrate creating different schedulers
    for name, config in configs.items():
        try:
            scheduler = factory.create_scheduler(name, config)
            print(f"✓ Created {name} scheduler: {type(scheduler).__name__}")
        except Exception as e:
            print(f"✗ Failed to create {name} scheduler: {e}")

    print()
    print("Benefits of this approach:")
    print("1. Adding new schedulers doesn't require modifying existing code")
    print("2. Each scheduler is independently testable")
    print("3. Configuration is type-safe and validated")
    print("4. Easy to discover available schedulers at runtime")
    print("5. Clear separation between scheduler logic and training logic")


if __name__ == "__main__":
    demonstrate_ocp_improvement()
