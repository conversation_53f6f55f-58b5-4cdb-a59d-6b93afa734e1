"""
SOLID Principles Demonstration: Single Responsibility Principle (SRP)

This file demonstrates how to refactor the monolithic `pre_process_data` function
into focused, single-responsibility classes that are easier to test, maintain, and extend.

SOLID Principle: Single Responsibility Principle (SRP)
- Each class has one clear reason to change
- Responsibilities are clearly separated
- Components can be tested and modified independently

Original Issue: The `pre_process_data` function in funcs.py handles multiple responsibilities:
- Computing deviatoric stress
- Computing plastic strain invariants
- Computing free energy targets
- Filtering data based on plasticity threshold
- Data validation and reshaping

Improvement: Split into focused classes with single responsibilities.

Date: 2025-07-22
Author: Augment Agent (Demonstration)
"""

from abc import ABC, abstractmethod
from typing import Tuple, Optional
import numpy as np
from dataclasses import dataclass


# ============================================================================
# CONFIGURATION AND DATA STRUCTURES
# ============================================================================

@dataclass
class MaterialProperties:
    """Encapsulates material properties for calculations."""
    E: float = 2e5  # Young's modulus
    nu: float = 0.3  # Poisson's ratio


@dataclass
class PreprocessingConfig:
    """Configuration for data preprocessing pipeline."""
    plasticity_threshold: float = 1e-8
    global_dtype: np.dtype = np.float32


# ============================================================================
# SINGLE RESPONSIBILITY CLASSES
# ============================================================================

class DeviatoricStressCalculator:
    """
    Single Responsibility: Calculate deviatoric stress components.

    This class handles only the mathematical computation of deviatoric stress,
    making it easy to test and modify independently.
    """

    def compute_deviatoric(self, stress_tensor: np.ndarray) -> np.ndarray:
        """
        Compute deviatoric part of stress tensor.

        Args:
            stress_tensor: Array of shape (n, 6) with stress components

        Returns:
            Deviatoric stress tensor of shape (n, 6)
        """
        if stress_tensor.shape[1] != 6:
            raise ValueError(f"Expected 6 stress components, got {stress_tensor.shape[1]}")

        deviatoric = stress_tensor.copy()

        for i in range(len(stress_tensor)):
            hydrostatic = (stress_tensor[i, 0] + stress_tensor[i, 1] + stress_tensor[i, 2]) / 3.0
            deviatoric[i, 0] -= hydrostatic
            deviatoric[i, 1] -= hydrostatic
            deviatoric[i, 2] -= hydrostatic

        return deviatoric

    def reduce_deviatoric_components(self, deviatoric_stress: np.ndarray) -> np.ndarray:
        """
        Reduce deviatoric stress to independent components.

        Since s11 + s22 + s33 = 0, we can drop s33 and keep only independent components.

        Args:
            deviatoric_stress: Full deviatoric stress tensor (n, 6)

        Returns:
            Reduced deviatoric stress (n, 5) - [s11, s22, s12, s23, s13]
        """
        return np.hstack((deviatoric_stress[:, 0:2], deviatoric_stress[:, 3:]))


class PlasticStrainInvariantCalculator:
    """
    Single Responsibility: Calculate plastic strain invariants.

    Handles the complex computation of accumulated plastic strain invariants
    from incremental plastic strain data.
    """

    def __init__(self, material_props: MaterialProperties):
        self.material_props = material_props

    def compute_plastic_strain_increments(self, strain: np.ndarray, stress: np.ndarray) -> np.ndarray:
        """
        Compute incremental plastic strain from total strain and stress.

        Args:
            strain: Total strain tensor (n, 6)
            stress: Stress tensor (n, 6)

        Returns:
            Incremental plastic strain (n, 6)
        """
        compliance_matrix = self._get_compliance_matrix()

        delta_stress = np.diff(stress, axis=0)
        delta_strain_elastic = np.matmul(delta_stress, compliance_matrix.T)
        delta_strain_total = np.diff(strain, axis=0)
        delta_plastic_strain = delta_strain_total - delta_strain_elastic

        # Add zero initial increment
        return np.vstack((np.zeros((1, 6)), delta_plastic_strain))

    def compute_accumulated_invariants(self, plastic_strain_increments: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Compute accumulated plastic strain invariants.

        Args:
            plastic_strain_increments: Incremental plastic strain (n, 6)

        Returns:
            Tuple of (I1, I2, I3) accumulated invariants
        """
        n_samples = len(plastic_strain_increments)
        I1_acc = np.zeros(n_samples)
        I2_acc = np.zeros(n_samples)
        I3_acc = np.zeros(n_samples)

        I1_sum, I2_sum, I3_sum = 0, 0, 0

        for i in range(n_samples):
            I1_inc, I2_inc, I3_inc = self._compute_invariants(plastic_strain_increments[i].reshape(1, 6))
            I1_sum += I1_inc
            I2_sum += I2_inc
            I3_sum += I3_inc

            I1_acc[i] = I1_sum
            I2_acc[i] = I2_sum
            I3_acc[i] = I3_sum

        return I1_acc, I2_acc, I3_acc

    def _get_compliance_matrix(self) -> np.ndarray:
        """Get compliance matrix for elastic strain calculation."""
        E, nu = self.material_props.E, self.material_props.nu
        return np.array([
            [1, -nu, -nu, 0, 0, 0],
            [-nu, 1, -nu, 0, 0, 0],
            [-nu, -nu, 1, 0, 0, 0],
            [0, 0, 0, 2*(1+nu), 0, 0],
            [0, 0, 0, 0, 2*(1+nu), 0],
            [0, 0, 0, 0, 0, 2*(1+nu)]
        ]) / E

    def _compute_invariants(self, tensor: np.ndarray) -> Tuple[float, float, float]:
        """Compute tensor invariants for a single tensor."""
        I1 = tensor[0, 0] + tensor[0, 1] + tensor[0, 2]
        I2 = (tensor[0, 0]*tensor[0, 1] + tensor[0, 1]*tensor[0, 2] + tensor[0, 2]*tensor[0, 0] -
              (tensor[0, 3]**2 + tensor[0, 4]**2 + tensor[0, 5]**2))
        I3 = (tensor[0, 0]*tensor[0, 1]*tensor[0, 2] + 2*tensor[0, 3]*tensor[0, 4]*tensor[0, 5] -
              tensor[0, 0]*tensor[0, 3]**2 - tensor[0, 1]*tensor[0, 4]**2 - tensor[0, 2]*tensor[0, 5]**2)
        return I1, I2, I3


class FreeEnergyCalculator:
    """
    Single Responsibility: Calculate free energy and maximum free energy targets.

    Handles the computation of elastic free energy and its maximum accumulation.
    """

    def __init__(self, material_props: MaterialProperties):
        self.material_props = material_props

    def compute_free_energy(self, stress: np.ndarray) -> np.ndarray:
        """
        Compute elastic free energy from stress tensor.

        Args:
            stress: Stress tensor (n, 6)

        Returns:
            Free energy values (n,)
        """
        compliance_matrix = self._get_compliance_matrix()
        return 0.5 * np.einsum('ij,jk,ik->i', stress, compliance_matrix, stress)

    def compute_maximum_free_energy(self, free_energy: np.ndarray) -> np.ndarray:
        """
        Compute accumulated maximum free energy.

        Args:
            free_energy: Free energy values (n,)

        Returns:
            Maximum accumulated free energy (n,)
        """
        return np.maximum.accumulate(free_energy)

    def _get_compliance_matrix(self) -> np.ndarray:
        """Get compliance matrix for free energy calculation."""
        E, nu = self.material_props.E, self.material_props.nu
        return np.array([
            [1, -nu, -nu, 0, 0, 0],
            [-nu, 1, -nu, 0, 0, 0],
            [-nu, -nu, 1, 0, 0, 0],
            [0, 0, 0, 2*(1+nu), 0, 0],
            [0, 0, 0, 0, 2*(1+nu), 0],
            [0, 0, 0, 0, 0, 2*(1+nu)]
        ]) / E


class PlasticityDataFilter:
    """
    Single Responsibility: Filter data based on plasticity onset.

    Identifies the first plasticity step and filters data accordingly.
    """

    def find_plasticity_onset(self, accumulated_plastic_strain: np.ndarray, threshold: float) -> int:
        """
        Find the index where plasticity first occurs.

        Args:
            accumulated_plastic_strain: Accumulated equivalent plastic strain
            threshold: Threshold for detecting plasticity

        Returns:
            Index of first plastic step
        """
        return np.argmax(accumulated_plastic_strain > threshold)

    def filter_post_plasticity_data(self, data_arrays: list, onset_index: int) -> list:
        """
        Filter multiple data arrays to include only post-plasticity data.

        Args:
            data_arrays: List of numpy arrays to filter
            onset_index: Index from which to start filtering

        Returns:
            List of filtered arrays
        """
        return [array[onset_index:] for array in data_arrays]


# ============================================================================
# ORCHESTRATOR CLASS (FACADE PATTERN)
# ============================================================================

class DataPreprocessor:
    """
    Orchestrates the data preprocessing pipeline using single-responsibility components.

    This class demonstrates how to compose focused components while maintaining
    a clean interface for clients. It follows the Facade pattern to hide complexity.

    Benefits over original monolithic function:
    1. Each component can be tested independently
    2. Easy to swap implementations (e.g., different invariant calculations)
    3. Clear separation of concerns
    4. Easier to debug and maintain
    5. Components can be reused in other contexts
    """

    def __init__(self, material_props: MaterialProperties, config: PreprocessingConfig):
        self.material_props = material_props
        self.config = config

        # Initialize single-responsibility components
        self.stress_calculator = DeviatoricStressCalculator()
        self.invariant_calculator = PlasticStrainInvariantCalculator(material_props)
        self.energy_calculator = FreeEnergyCalculator(material_props)
        self.data_filter = PlasticityDataFilter()

    def preprocess(self, raw_data: np.ndarray) -> np.ndarray:
        """
        Main preprocessing pipeline that orchestrates all components.

        Args:
            raw_data: Raw data array with shape (n, 13)
                     Columns 0-5: strain tensor
                     Columns 6-11: stress tensor
                     Column 12: accumulated equivalent plastic strain

        Returns:
            Preprocessed data array with shape (m, 9) where m <= n
            Columns 0-2: plastic strain invariants (I1, I2, I3)
            Columns 3-7: deviatoric stress components (5 independent)
            Column 8: maximum free energy
        """
        # Extract components from raw data
        strain = raw_data[:, 0:6]
        stress = raw_data[:, 6:12]
        acc_plastic_strain = raw_data[:, 12]

        # Step 1: Calculate deviatoric stress (reduced to independent components)
        deviatoric_stress = self.stress_calculator.compute_deviatoric(stress)
        reduced_stress = self.stress_calculator.reduce_deviatoric_components(deviatoric_stress)

        # Step 2: Calculate plastic strain invariants
        plastic_increments = self.invariant_calculator.compute_plastic_strain_increments(strain, stress)
        I1, I2, I3 = self.invariant_calculator.compute_accumulated_invariants(plastic_increments)

        # Step 3: Calculate free energy targets
        free_energy = self.energy_calculator.compute_free_energy(stress)
        max_free_energy = self.energy_calculator.compute_maximum_free_energy(free_energy)

        # Step 4: Filter for post-plasticity data
        onset_index = self.data_filter.find_plasticity_onset(acc_plastic_strain, self.config.plasticity_threshold)

        filtered_data = self.data_filter.filter_post_plasticity_data([
            I1, I2, I3, reduced_stress, max_free_energy
        ], onset_index)

        I1_filtered, I2_filtered, I3_filtered, stress_filtered, energy_filtered = filtered_data

        # Step 5: Assemble final preprocessed data
        n_samples = len(I1_filtered)
        preprocessed_data = np.empty((n_samples, 9), dtype=self.config.global_dtype)

        preprocessed_data[:, 0] = I1_filtered
        preprocessed_data[:, 1] = I2_filtered
        preprocessed_data[:, 2] = I3_filtered
        preprocessed_data[:, 3:8] = stress_filtered
        preprocessed_data[:, 8] = energy_filtered

        return preprocessed_data


# ============================================================================
# USAGE EXAMPLE AND COMPARISON
# ============================================================================

def demonstrate_srp_improvement():
    """
    Demonstrates the improved SRP-compliant data preprocessing.

    This function shows how the refactored code provides the same functionality
    as the original pre_process_data function but with better architecture.
    """
    # Configuration
    material_props = MaterialProperties(E=2e5, nu=0.3)
    config = PreprocessingConfig(plasticity_threshold=1e-8)

    # Create preprocessor with composed components
    preprocessor = DataPreprocessor(material_props, config)

    # Example usage (would use real data in practice)
    # raw_data = get_raw_data('12.5', 2e5)  # From original codebase
    # preprocessed_data = preprocessor.preprocess(raw_data)

    print("SRP Demonstration Complete!")
    print("Benefits:")
    print("1. Each class has a single, clear responsibility")
    print("2. Components can be tested independently")
    print("3. Easy to modify or extend individual calculations")
    print("4. Clear separation between mathematical operations and data flow")
    print("5. Reusable components for other preprocessing needs")


if __name__ == "__main__":
    demonstrate_srp_improvement()
