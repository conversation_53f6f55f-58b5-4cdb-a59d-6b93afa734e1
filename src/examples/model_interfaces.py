"""
SOLID Principles Demonstration: Interface Segregation Principle (ISP)

This file demonstrates how to separate model concerns into focused interfaces,
ensuring clients only depend on the methods they actually use.

SOLID Principle: Interface Segregation Principle (ISP)
- Clients should not be forced to depend on interfaces they don't use
- Many client-specific interfaces are better than one general-purpose interface
- Interfaces should be cohesive and focused

Original Issue: The NN_Model class in nn_model.py combines multiple responsibilities:
- Model architecture definition
- Training logic
- Model saving and metadata extraction
- Configuration management

This forces all clients to depend on methods they don't need.

Improvement: Split into focused interfaces and implementations.

Date: 2025-07-22
Author: Augment Agent (Demonstration)
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
import numpy as np
import tensorflow as tf
import json
import os
from datetime import datetime


# ============================================================================
# CONFIGURATION AND DATA STRUCTURES
# ============================================================================

@dataclass
class ModelArchitectureConfig:
    """Configuration for model architecture."""
    num_inputs: int = 12
    num_outputs: int = 1
    hidden_dims: List[int] = None
    activation_fn: Any = tf.keras.activations.relu
    dtype: tf.DType = tf.float64
    
    def __post_init__(self):
        if self.hidden_dims is None:
            self.hidden_dims = [48]


@dataclass
class TrainingConfig:
    """Configuration for model training."""
    learning_rate: float = 3e-4
    epochs: int = 25
    batch_size: int = 32
    validation_split: float = 0.0
    early_stopping_patience: int = 500
    verbose: int = 1


@dataclass
class ModelMetadata:
    """Metadata for trained models."""
    dataset_name: Optional[str] = None
    training_config: Optional[TrainingConfig] = None
    architecture_config: Optional[ModelArchitectureConfig] = None
    normalization_params: Optional[np.ndarray] = None
    training_history: Optional[Dict] = None
    created_at: Optional[str] = None


# ============================================================================
# FOCUSED INTERFACES (ISP COMPLIANCE)
# ============================================================================

class ModelArchitecture(ABC):
    """
    Interface for model architecture definition.
    
    Clients that only need to define or inspect model architecture
    depend only on this interface.
    """
    
    @abstractmethod
    def build_model(self, config: ModelArchitectureConfig) -> tf.keras.Model:
        """Build and return the model architecture."""
        pass
    
    @abstractmethod
    def get_model_summary(self, model: tf.keras.Model) -> str:
        """Get a string summary of the model architecture."""
        pass


class ModelTrainer(ABC):
    """
    Interface for model training operations.
    
    Clients that only need to train models depend only on this interface.
    """
    
    @abstractmethod
    def train(self, model: tf.keras.Model, train_data: np.ndarray, 
              val_data: np.ndarray, config: TrainingConfig) -> Dict[str, List]:
        """Train the model and return training history."""
        pass
    
    @abstractmethod
    def evaluate(self, model: tf.keras.Model, test_data: np.ndarray) -> Dict[str, float]:
        """Evaluate the model on test data."""
        pass


class ModelPersistence(ABC):
    """
    Interface for model saving and loading operations.
    
    Clients that only need to save/load models depend only on this interface.
    """
    
    @abstractmethod
    def save_model(self, model: tf.keras.Model, metadata: ModelMetadata, 
                   save_path: str) -> str:
        """Save model and metadata to specified path."""
        pass
    
    @abstractmethod
    def load_model(self, model_path: str) -> Tuple[tf.keras.Model, ModelMetadata]:
        """Load model and metadata from specified path."""
        pass


class ModelPredictor(ABC):
    """
    Interface for model inference operations.
    
    Clients that only need to make predictions depend only on this interface.
    """
    
    @abstractmethod
    def predict(self, model: tf.keras.Model, inputs: np.ndarray) -> np.ndarray:
        """Make predictions using the model."""
        pass
    
    @abstractmethod
    def predict_batch(self, model: tf.keras.Model, inputs: np.ndarray, 
                     batch_size: int = 32) -> np.ndarray:
        """Make batch predictions using the model."""
        pass


class ModelValidator(ABC):
    """
    Interface for model validation operations.
    
    Clients that only need to validate models depend only on this interface.
    """
    
    @abstractmethod
    def validate_inputs(self, inputs: np.ndarray, expected_shape: Tuple[int, ...]) -> bool:
        """Validate input data shape and type."""
        pass
    
    @abstractmethod
    def validate_model_config(self, config: ModelArchitectureConfig) -> bool:
        """Validate model configuration parameters."""
        pass


# ============================================================================
# CONCRETE IMPLEMENTATIONS
# ============================================================================

class MaterialsModelArchitecture(ModelArchitecture):
    """
    Concrete implementation of model architecture for materials modeling.
    
    This class focuses solely on defining the neural network architecture
    for materials science applications.
    """
    
    def build_model(self, config: ModelArchitectureConfig) -> tf.keras.Model:
        """Build the materials modeling neural network."""
        # Set global dtype
        tf.keras.backend.set_floatx('float64' if config.dtype == tf.float64 else 'float32')
        
        # Input layer
        inputs = tf.keras.layers.Input(shape=(config.num_inputs,), dtype=config.dtype)
        
        # Hidden layers
        x = inputs
        for i, units in enumerate(config.hidden_dims):
            x = tf.keras.layers.Dense(
                units,
                activation=config.activation_fn,
                name=f'hidden_layer_{i+1}',
                dtype=config.dtype
            )(x)
        
        # Output layer
        outputs = tf.keras.layers.Dense(
            config.num_outputs,
            name='output_layer',
            dtype=config.dtype
        )(x)
        
        model = tf.keras.Model(inputs=inputs, outputs=outputs)
        return model
    
    def get_model_summary(self, model: tf.keras.Model) -> str:
        """Get model architecture summary."""
        import io
        import sys
        
        # Capture model.summary() output
        old_stdout = sys.stdout
        sys.stdout = buffer = io.StringIO()
        model.summary()
        sys.stdout = old_stdout
        
        return buffer.getvalue()


class StandardModelTrainer(ModelTrainer):
    """
    Concrete implementation of model training for standard neural networks.
    
    This class focuses solely on training operations and can be easily
    extended or replaced without affecting other components.
    """
    
    def train(self, model: tf.keras.Model, train_data: np.ndarray, 
              val_data: np.ndarray, config: TrainingConfig) -> Dict[str, List]:
        """Train the model with the given configuration."""
        
        # Setup optimizer
        optimizer = tf.keras.optimizers.Nadam(learning_rate=config.learning_rate)
        
        # Compile model
        model.compile(optimizer=optimizer, loss='mae', metrics=['mape'])
        
        # Setup callbacks
        callbacks = []
        if config.early_stopping_patience > 0:
            early_stopping = tf.keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=config.early_stopping_patience,
                restore_best_weights=True
            )
            callbacks.append(early_stopping)
        
        # Train model
        history = model.fit(
            x=train_data[:, :-1],  # All columns except last (target)
            y=train_data[:, -1],   # Last column (target)
            validation_data=(val_data[:, :-1], val_data[:, -1]),
            epochs=config.epochs,
            batch_size=config.batch_size,
            callbacks=callbacks,
            verbose=config.verbose
        )
        
        return history.history
    
    def evaluate(self, model: tf.keras.Model, test_data: np.ndarray) -> Dict[str, float]:
        """Evaluate model performance on test data."""
        results = model.evaluate(
            x=test_data[:, :-1],
            y=test_data[:, -1],
            verbose=0
        )
        
        # Return metrics as dictionary
        metric_names = model.metrics_names
        return dict(zip(metric_names, results))


class FileSystemModelPersistence(ModelPersistence):
    """
    Concrete implementation of model persistence using file system.
    
    This class focuses solely on saving and loading models,
    making it easy to swap for different storage backends.
    """
    
    def save_model(self, model: tf.keras.Model, metadata: ModelMetadata, 
                   save_path: str) -> str:
        """Save model and metadata to file system."""
        
        # Create directory if it doesn't exist
        os.makedirs(save_path, exist_ok=True)
        
        # Save model
        model_path = os.path.join(save_path, 'model')
        model.save(model_path)
        
        # Save metadata
        metadata_dict = self._metadata_to_dict(metadata)
        metadata_path = os.path.join(save_path, 'metadata.json')
        
        with open(metadata_path, 'w') as f:
            json.dump(metadata_dict, f, indent=4, default=str)
        
        return save_path
    
    def load_model(self, model_path: str) -> Tuple[tf.keras.Model, ModelMetadata]:
        """Load model and metadata from file system."""
        
        # Load model
        model_file = os.path.join(model_path, 'model')
        model = tf.keras.models.load_model(model_file)
        
        # Load metadata
        metadata_file = os.path.join(model_path, 'metadata.json')
        with open(metadata_file, 'r') as f:
            metadata_dict = json.load(f)
        
        metadata = self._dict_to_metadata(metadata_dict)
        
        return model, metadata
    
    def _metadata_to_dict(self, metadata: ModelMetadata) -> Dict:
        """Convert metadata object to dictionary for JSON serialization."""
        result = {}
        
        if metadata.dataset_name:
            result['dataset_name'] = metadata.dataset_name
        if metadata.training_config:
            result['training_config'] = metadata.training_config.__dict__
        if metadata.architecture_config:
            result['architecture_config'] = {
                k: v for k, v in metadata.architecture_config.__dict__.items()
                if k != 'activation_fn'  # Skip non-serializable activation function
            }
        if metadata.normalization_params is not None:
            result['normalization_params'] = metadata.normalization_params.tolist()
        if metadata.training_history:
            result['training_history'] = metadata.training_history
        if metadata.created_at:
            result['created_at'] = metadata.created_at
        
        return result
    
    def _dict_to_metadata(self, data: Dict) -> ModelMetadata:
        """Convert dictionary to metadata object."""
        metadata = ModelMetadata()
        
        metadata.dataset_name = data.get('dataset_name')
        metadata.created_at = data.get('created_at')
        metadata.training_history = data.get('training_history')
        
        if 'normalization_params' in data:
            metadata.normalization_params = np.array(data['normalization_params'])
        
        if 'training_config' in data:
            metadata.training_config = TrainingConfig(**data['training_config'])
        
        if 'architecture_config' in data:
            metadata.architecture_config = ModelArchitectureConfig(**data['architecture_config'])
        
        return metadata


class StandardModelPredictor(ModelPredictor):
    """
    Concrete implementation of model prediction operations.
    
    This class focuses solely on making predictions,
    separate from training and persistence concerns.
    """
    
    def predict(self, model: tf.keras.Model, inputs: np.ndarray) -> np.ndarray:
        """Make predictions using the model."""
        return model.predict(inputs, verbose=0)
    
    def predict_batch(self, model: tf.keras.Model, inputs: np.ndarray, 
                     batch_size: int = 32) -> np.ndarray:
        """Make batch predictions using the model."""
        return model.predict(inputs, batch_size=batch_size, verbose=0)


class StandardModelValidator(ModelValidator):
    """
    Concrete implementation of model validation operations.

    This class focuses solely on validation logic,
    separate from other model operations.
    """

    def validate_inputs(self, inputs: np.ndarray, expected_shape: Tuple[int, ...]) -> bool:
        """Validate input data shape and type."""
        if not isinstance(inputs, np.ndarray):
            return False

        if len(expected_shape) == 2 and inputs.ndim == 2:
            return inputs.shape[1] == expected_shape[1]

        return inputs.shape == expected_shape

    def validate_model_config(self, config: ModelArchitectureConfig) -> bool:
        """Validate model configuration parameters."""
        if config.num_inputs <= 0 or config.num_outputs <= 0:
            return False

        if not config.hidden_dims or any(dim <= 0 for dim in config.hidden_dims):
            return False

        return True


# ============================================================================
# FACADE FOR SIMPLIFIED USAGE
# ============================================================================

class MaterialsModelFacade:
    """
    Facade that provides a simplified interface for common model operations.

    This demonstrates how ISP-compliant interfaces can be composed to provide
    a convenient API for clients who need multiple operations, while still
    allowing clients to use individual interfaces when they only need specific functionality.

    Benefits:
    1. Clients can choose the level of interface they need
    2. Each interface can be tested independently
    3. Easy to swap implementations (e.g., different storage backends)
    4. Clear separation of concerns
    5. Follows both ISP and Single Responsibility Principle
    """

    def __init__(self,
                 architecture: ModelArchitecture = None,
                 trainer: ModelTrainer = None,
                 persistence: ModelPersistence = None,
                 predictor: ModelPredictor = None,
                 validator: ModelValidator = None):

        # Use default implementations if none provided
        self.architecture = architecture or MaterialsModelArchitecture()
        self.trainer = trainer or StandardModelTrainer()
        self.persistence = persistence or FileSystemModelPersistence()
        self.predictor = predictor or StandardModelPredictor()
        self.validator = validator or StandardModelValidator()

    def create_and_train_model(self,
                              architecture_config: ModelArchitectureConfig,
                              training_config: TrainingConfig,
                              train_data: np.ndarray,
                              val_data: np.ndarray,
                              normalization_params: np.ndarray = None) -> Tuple[tf.keras.Model, Dict]:
        """
        Complete workflow: create, validate, and train a model.

        This method demonstrates how the facade can orchestrate multiple
        focused interfaces to provide complex functionality.
        """

        # Validate configuration
        if not self.validator.validate_model_config(architecture_config):
            raise ValueError("Invalid model architecture configuration")

        # Validate input data
        expected_shape = (None, architecture_config.num_inputs)
        if not self.validator.validate_inputs(train_data[:, :-1], expected_shape):
            raise ValueError("Invalid training data shape")

        # Build model
        model = self.architecture.build_model(architecture_config)

        # Train model
        history = self.trainer.train(model, train_data, val_data, training_config)

        return model, history

    def save_trained_model(self,
                          model: tf.keras.Model,
                          save_path: str,
                          dataset_name: str = None,
                          training_config: TrainingConfig = None,
                          architecture_config: ModelArchitectureConfig = None,
                          normalization_params: np.ndarray = None,
                          training_history: Dict = None) -> str:
        """Save a trained model with metadata."""

        metadata = ModelMetadata(
            dataset_name=dataset_name,
            training_config=training_config,
            architecture_config=architecture_config,
            normalization_params=normalization_params,
            training_history=training_history,
            created_at=datetime.now().isoformat()
        )

        return self.persistence.save_model(model, metadata, save_path)

    def load_and_predict(self, model_path: str, inputs: np.ndarray) -> Tuple[np.ndarray, ModelMetadata]:
        """Load a model and make predictions."""

        model, metadata = self.persistence.load_model(model_path)
        predictions = self.predictor.predict(model, inputs)

        return predictions, metadata


# ============================================================================
# USAGE EXAMPLE AND COMPARISON
# ============================================================================

def demonstrate_isp_improvement():
    """
    Demonstrates the improved ISP-compliant model architecture.

    Shows how clients can depend only on the interfaces they need,
    and how the system is more modular and testable.
    """
    print("=== Interface Segregation Principle Demonstration ===")
    print()

    # Example 1: Client only needs to make predictions
    print("1. Client only needs predictions (depends only on ModelPredictor):")
    predictor = StandardModelPredictor()
    # In real usage: predictions = predictor.predict(loaded_model, input_data)
    print("   ✓ Client doesn't depend on training, saving, or architecture interfaces")
    print()

    # Example 2: Client only needs to save models
    print("2. Client only needs to save models (depends only on ModelPersistence):")
    persistence = FileSystemModelPersistence()
    # In real usage: persistence.save_model(model, metadata, save_path)
    print("   ✓ Client doesn't depend on training, prediction, or architecture interfaces")
    print()

    # Example 3: Client needs full workflow (uses facade)
    print("3. Client needs full workflow (uses facade with all interfaces):")
    facade = MaterialsModelFacade()

    # Example configurations
    arch_config = ModelArchitectureConfig(
        num_inputs=12,
        num_outputs=1,
        hidden_dims=[48, 24],
        activation_fn=tf.keras.activations.relu
    )

    train_config = TrainingConfig(
        learning_rate=1e-3,
        epochs=10,
        batch_size=32
    )

    print("   ✓ Facade orchestrates multiple focused interfaces")
    print("   ✓ Each interface can be swapped independently")
    print()

    print("Benefits of this approach:")
    print("1. Clients depend only on methods they actually use")
    print("2. Each interface is focused and cohesive")
    print("3. Easy to test individual components")
    print("4. Easy to swap implementations (e.g., different storage backends)")
    print("5. Clear separation of concerns")
    print("6. Supports both simple and complex usage patterns")


if __name__ == "__main__":
    demonstrate_isp_improvement()
