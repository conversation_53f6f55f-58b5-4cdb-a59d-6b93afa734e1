"""
SOLID Principles Integration Examples

This file demonstrates how the SOLID-compliant components work together
to provide the same functionality as the original codebase, but with
better architecture, maintainability, and extensibility.

This shows practical integration of:
1. SRP-compliant data preprocessing
2. OCP-compliant learning rate scheduling  
3. ISP-compliant model components

Date: 2025-07-22
Author: Augment Agent (Demonstration)
"""

import numpy as np
import tensorflow as tf
from typing import Dict, Any

# Import our SOLID-compliant components
from data_preprocessor import (
    DataPreprocessor, MaterialProperties, PreprocessingConfig
)
from learning_rate_schedulers import (
    SchedulerFactory, ExponentialConfig, CosineConfig, SchedulerConfig
)
from model_interfaces import (
    MaterialsModelFacade, ModelArchitectureConfig, TrainingConfig,
    MaterialsModelArchitecture, StandardModelTrainer
)


class IntegratedMaterialsModelingPipeline:
    """
    Demonstrates integration of all SOLID-compliant components.
    
    This class shows how the refactored components can work together
    to provide the same functionality as the original codebase, but
    with better separation of concerns and extensibility.
    """
    
    def __init__(self):
        # Initialize components following dependency injection
        self.scheduler_factory = SchedulerFactory()
        self.model_facade = MaterialsModelFacade()
        
        # Material properties (could be injected)
        self.material_props = MaterialProperties(E=2e5, nu=0.3)
        
    def run_complete_training_pipeline(self, 
                                     train_dataset_name: str = '12.5',
                                     val_dataset_name: str = '12.3',
                                     scheduler_type: str = 'exponential',
                                     save_model: bool = True) -> Dict[str, Any]:
        """
        Complete training pipeline using SOLID-compliant components.
        
        This method demonstrates how the refactored architecture provides
        the same functionality as the original train_model.py script.
        """
        
        print("=== Integrated Materials Modeling Pipeline ===")
        print(f"Training dataset: {train_dataset_name}")
        print(f"Validation dataset: {val_dataset_name}")
        print(f"Scheduler type: {scheduler_type}")
        print()
        
        # Step 1: Data Preprocessing (SRP-compliant)
        print("1. Data Preprocessing...")
        train_data, val_data = self._load_and_preprocess_data(
            train_dataset_name, val_dataset_name
        )
        print(f"   ✓ Training data shape: {train_data.shape}")
        print(f"   ✓ Validation data shape: {val_data.shape}")
        print()
        
        # Step 2: Configure Model Architecture
        print("2. Model Architecture Configuration...")
        arch_config = ModelArchitectureConfig(
            num_inputs=12,
            num_outputs=1,
            hidden_dims=[48],
            activation_fn=tf.keras.activations.relu,
            dtype=tf.float64
        )
        print(f"   ✓ Architecture: {arch_config.hidden_dims} hidden units")
        print()
        
        # Step 3: Configure Training with Learning Rate Scheduling (OCP-compliant)
        print("3. Training Configuration with Learning Rate Scheduling...")
        train_config, scheduler = self._configure_training_with_scheduler(
            scheduler_type, epochs=25, learning_rate=3e-4
        )
        print(f"   ✓ Scheduler: {scheduler_type}")
        print(f"   ✓ Learning rate: {train_config.learning_rate}")
        print(f"   ✓ Epochs: {train_config.epochs}")
        print()
        
        # Step 4: Train Model (ISP-compliant)
        print("4. Model Training...")
        model, history = self._train_model_with_scheduler(
            arch_config, train_config, train_data, val_data, scheduler
        )
        print(f"   ✓ Training completed in {len(history['loss'])} epochs")
        print(f"   ✓ Final training loss: {history['loss'][-1]:.6f}")
        print(f"   ✓ Final validation loss: {history['val_loss'][-1]:.6f}")
        print()
        
        # Step 5: Save Model (if requested)
        model_path = None
        if save_model:
            print("5. Model Saving...")
            model_path = self._save_model_with_metadata(
                model, train_dataset_name, arch_config, train_config, history
            )
            print(f"   ✓ Model saved to: {model_path}")
            print()
        
        return {
            'model': model,
            'history': history,
            'model_path': model_path,
            'train_data_shape': train_data.shape,
            'val_data_shape': val_data.shape
        }
    
    def _load_and_preprocess_data(self, train_name: str, val_name: str):
        """Load and preprocess data using SRP-compliant components."""
        
        # This would use the actual data loading in practice
        # For demonstration, we'll create mock data with correct structure
        print("   Loading raw data...")
        
        # Mock data generation (in practice, would use get_raw_data)
        n_train, n_val = 1000, 500
        
        # Generate mock raw data with shape (n, 13)
        # Columns 0-5: strain, 6-11: stress, 12: accumulated plastic strain
        train_raw = np.random.randn(n_train, 13).astype(np.float32)
        val_raw = np.random.randn(n_val, 13).astype(np.float32)
        
        # Make accumulated plastic strain realistic (increasing)
        train_raw[:, 12] = np.cumsum(np.abs(train_raw[:, 12])) * 1e-6
        val_raw[:, 12] = np.cumsum(np.abs(val_raw[:, 12])) * 1e-6
        
        print("   Preprocessing data...")
        
        # Use SRP-compliant preprocessor
        config = PreprocessingConfig(plasticity_threshold=1e-8)
        preprocessor = DataPreprocessor(self.material_props, config)
        
        train_processed = preprocessor.preprocess(train_raw)
        val_processed = preprocessor.preprocess(val_raw)
        
        return train_processed, val_processed
    
    def _configure_training_with_scheduler(self, scheduler_type: str, 
                                         epochs: int, learning_rate: float):
        """Configure training with OCP-compliant learning rate scheduling."""
        
        # Create scheduler configuration
        if scheduler_type == 'exponential':
            scheduler_config = ExponentialConfig(
                initial_learning_rate=learning_rate,
                total_epochs=epochs,
                decay_rate=0.75,
                decay_fraction=0.5
            )
        elif scheduler_type == 'cosine':
            scheduler_config = CosineConfig(
                initial_learning_rate=learning_rate,
                total_epochs=epochs,
                first_decay_fraction=0.2
            )
        else:
            scheduler_config = SchedulerConfig(
                initial_learning_rate=learning_rate,
                total_epochs=epochs
            )
        
        # Create scheduler using factory
        scheduler = self.scheduler_factory.create_scheduler(scheduler_type, scheduler_config)
        
        # Create training configuration
        train_config = TrainingConfig(
            learning_rate=learning_rate,  # Will be overridden by scheduler
            epochs=epochs,
            batch_size=32,
            early_stopping_patience=500,
            verbose=1
        )
        
        return train_config, scheduler
    
    def _train_model_with_scheduler(self, arch_config, train_config, 
                                   train_data, val_data, scheduler):
        """Train model using ISP-compliant components with custom scheduler."""
        
        # Build model using architecture interface
        architecture = MaterialsModelArchitecture()
        model = architecture.build_model(arch_config)
        
        # Create custom trainer that uses our scheduler
        trainer = CustomSchedulerTrainer(scheduler)
        
        # Train model
        history = trainer.train(model, train_data, val_data, train_config)
        
        return model, history
    
    def _save_model_with_metadata(self, model, dataset_name, arch_config, 
                                 train_config, history):
        """Save model with comprehensive metadata."""
        
        save_path = f"../../saved_models/solid_demo_{dataset_name}"
        
        return self.model_facade.save_trained_model(
            model=model,
            save_path=save_path,
            dataset_name=dataset_name,
            training_config=train_config,
            architecture_config=arch_config,
            training_history=history
        )


class CustomSchedulerTrainer(StandardModelTrainer):
    """
    Custom trainer that demonstrates extensibility of ISP-compliant design.
    
    This shows how easy it is to extend the base trainer with custom
    learning rate scheduling without modifying existing code.
    """
    
    def __init__(self, scheduler):
        self.scheduler = scheduler
    
    def train(self, model, train_data, val_data, config):
        """Train with custom learning rate scheduler."""
        
        # Setup optimizer with custom scheduler
        optimizer = tf.keras.optimizers.Nadam(learning_rate=self.scheduler)
        
        # Compile model
        model.compile(optimizer=optimizer, loss='mae', metrics=['mape'])
        
        # Setup callbacks
        callbacks = []
        if config.early_stopping_patience > 0:
            early_stopping = tf.keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=config.early_stopping_patience,
                restore_best_weights=True
            )
            callbacks.append(early_stopping)
        
        # Add learning rate logging callback
        class LRLogger(tf.keras.callbacks.Callback):
            def on_epoch_end(self, epoch, logs=None):
                if epoch % 10 == 0:
                    lr = self.model.optimizer.learning_rate
                    if hasattr(lr, '__call__'):
                        current_lr = lr(epoch).numpy()
                    else:
                        current_lr = float(lr)
                    print(f"Epoch {epoch}: Learning rate: {current_lr:.8f}")
        
        callbacks.append(LRLogger())
        
        # Train model
        history = model.fit(
            x=train_data[:, :-1],
            y=train_data[:, -1],
            validation_data=(val_data[:, :-1], val_data[:, -1]),
            epochs=config.epochs,
            batch_size=config.batch_size,
            callbacks=callbacks,
            verbose=config.verbose
        )
        
        return history.history


def demonstrate_integration():
    """
    Main demonstration function showing integrated SOLID-compliant pipeline.
    """
    
    # Create and run integrated pipeline
    pipeline = IntegratedMaterialsModelingPipeline()
    
    # Run with different scheduler types to show extensibility
    results = {}
    
    for scheduler_type in ['constant', 'exponential', 'cosine']:
        print(f"\n{'='*60}")
        print(f"Running pipeline with {scheduler_type} scheduler")
        print('='*60)
        
        try:
            result = pipeline.run_complete_training_pipeline(
                scheduler_type=scheduler_type,
                save_model=False  # Skip saving for demo
            )
            results[scheduler_type] = result
            print(f"✓ {scheduler_type} scheduler completed successfully")
            
        except Exception as e:
            print(f"✗ {scheduler_type} scheduler failed: {e}")
    
    print(f"\n{'='*60}")
    print("Integration Demonstration Summary")
    print('='*60)
    print("Benefits of SOLID-compliant architecture:")
    print("1. Easy to swap components (schedulers, data processors, etc.)")
    print("2. Each component can be tested independently")
    print("3. Clear separation of concerns")
    print("4. Extensible without modifying existing code")
    print("5. Maintains same functionality as original codebase")
    print("6. Better error handling and validation")


if __name__ == "__main__":
    # Set random seed for reproducible results
    tf.random.set_seed(42)
    np.random.seed(42)
    
    demonstrate_integration()
