from plots import Plotter
plotter = Plotter()

from funcs import get_raw_data, compute_psi, compute_invariants, compute_deviatoric, compute_eps_pl, pre_process_data
import numpy as np
raw_data = get_raw_data('1.0',2e5)
pre_processed_data = pre_process_data(raw_data)
val_data = pre_process_data(get_raw_data('12.5',2e5))
test_data = pre_process_data(get_raw_data('12.3',2e5))


plotter.plot_data_hisograms(pre_processed_data, val_data, test_data)


plotter.plot_data_scatter(pre_processed_data, axis_dict={'x':1, 'y':3, 'z':8})